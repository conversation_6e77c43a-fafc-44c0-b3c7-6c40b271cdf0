<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>航班选择</title>
    <link rel="stylesheet" href="../../css/common.css" />
    <link rel="stylesheet" href="../../css/flightOptions.css" />
  </head>
  <body>
    <div class="fo">
      <div class="fo-container">
        <%- include('../../modules/step/index') %>

        <div id="wrap-horizontal-calendar"></div>
      </div>
    </div>

    <script src="../../plugins/jquery-3.7.1.min.js"></script>
    <script src="../../plugins/dayjs.min.js"></script>
    <script src="/js/flightOptions.js"></script>

    <script>
      // 生成示例价格数据
      function generatePriceData(startDate, days) {
        const data = []
        const basePrice = 1920

        for (let i = 0; i < days; i++) {
          const date = new Date(startDate)
          date.setDate(date.getDate() + i)

          // 模拟价格波动，周末价格稍高
          const isWeekend = date.getDay() === 0 || date.getDay() === 6
          const weekendBonus = isWeekend ? 200 : 0
          const variation = Math.random() * 400 - 200 // -200 到 +200 的随机变化
          const price = Math.max(
            1200,
            Math.round(basePrice + variation + weekendBonus)
          )

          data.push({
            date: dayjs(date).format('YYYY-MM-DD'),
            price: price,
            currency: 'CNY',
          })
        }

        return data
      }

      $(document).ready(function () {
        const today = new Date()
        const priceData = generatePriceData(today, 60) // 生成60天的数据

        // 标准横向价格日历
        $('#wrap-horizontal-calendar').horizontalPriceCalendar({
          startDate: today,
          priceData: priceData,
          daysToShow: 7,
          showPriceCalendarButton: true,
          onDateSelect: function (date, priceInfo) {
            $('#standard-selected-info').show()
            $('#standard-date-text').text(dayjs(date).format('YYYY-MM-DD'))
            if (priceInfo) {
              $('#standard-price-text').text(
                `价格: ${
                  priceInfo.currency
                } ${priceInfo.price.toLocaleString()}`
              )
            } else {
              $('#standard-price-text').text('价格: 暂无数据')
            }
          },
        })
      })
    </script>
  </body>
</html>
