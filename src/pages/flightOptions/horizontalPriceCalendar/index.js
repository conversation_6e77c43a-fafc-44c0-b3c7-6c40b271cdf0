// 横向价格日历组件类
class HorizontalPriceCalendar {
  constructor(container, options = {}) {
    this.container = $(container)
    this.options = {
      // 默认配置
      startDate: new Date(),
      selectedDate: null,
      priceData: [], // 价格数据 [{date: '2024-06-15', price: 1920, currency: 'CNY'}]
      onDateSelect: null, // 日期选择回调
      showPriceCalendarButton: true, // 是否显示价格日历按钮
      daysToShow: 7, // 显示的天数
      ...options,
    }

    this.currentStartDate = new Date(this.options.startDate)
    this.selectedDate = this.options.selectedDate

    this.init()
  }

  init() {
    this.render()
    this.bindEvents()
  }

  // 渲染日历
  render() {
    const calendarHtml = this.generateCalendarHtml()
    this.container.html(calendarHtml)
  }

  // 生成日历HTML
  generateCalendarHtml() {
    const days = this.generateDaysData()

    let html = '<div class="horizontal-price-calendar">'

    // 导航按钮和价格日历按钮
    html += '<div class="horizontal-calendar-header">'
    html +=
      '<button class="calendar-nav-btn prev-btn" data-action="prev">&lt;</button>'

    if (this.options.showPriceCalendarButton) {
      html += '<button class="price-calendar-btn">价格日历</button>'
    }

    html +=
      '<button class="calendar-nav-btn next-btn" data-action="next">&gt;</button>'
    html += '</div>'

    // 日期列表
    html += '<div class="horizontal-calendar-days">'
    days.forEach((day) => {
      const dayClass = this.getDayClass(day)
      const priceInfo = this.getPriceInfo(day.date)

      html += `<div class="horizontal-calendar-day ${dayClass}" data-date="${this.formatDate(
        day.date
      )}">`

      // 货币和价格
      if (priceInfo) {
        html += `<div class="day-currency">${priceInfo.currency}</div>`
        html += `<div class="day-price">${priceInfo.price.toLocaleString()}</div>`
      } else {
        html += `<div class="day-currency">CNY</div>`
        html += `<div class="day-price">--</div>`
      }

      // 日期
      html += `<div class="day-date">${this.formatDayDate(day.date)}</div>`

      html += '</div>'
    })
    html += '</div>'

    html += '</div>'

    return html
  }

  // 生成日期数据
  generateDaysData() {
    const days = []

    for (let i = 0; i < this.options.daysToShow; i++) {
      const date = new Date(this.currentStartDate)
      date.setDate(date.getDate() + i)

      days.push({
        date: date,
        isToday: this.isToday(date),
        isPast: this.isPastDate(date),
      })
    }

    return days
  }

  // 获取日期的CSS类
  getDayClass(day) {
    let classes = []

    if (day.isToday) {
      classes.push('today')
    }

    if (this.isSelected(day.date)) {
      classes.push('selected')
    }

    if (day.isPast) {
      classes.push('past')
    }

    return classes.join(' ')
  }

  // 获取价格信息
  getPriceInfo(date) {
    const dateStr = this.formatDate(date)
    return this.options.priceData.find((item) => item.date === dateStr)
  }

  // 判断是否是今天
  isToday(date) {
    const today = new Date()
    return date.toDateString() === today.toDateString()
  }

  // 判断是否是选中日期
  isSelected(date) {
    if (!this.selectedDate) return false
    return date.toDateString() === this.selectedDate.toDateString()
  }

  // 判断是否是过去的日期
  isPastDate(date) {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return date < today
  }

  // 格式化日期 (YYYY-MM-DD)
  formatDate(date) {
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  }

  // 格式化日期显示 (MM-DD 星期)
  formatDayDate(date) {
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat']
    const weekday = weekdays[date.getDay()]
    return `${month}-${day} ${weekday}`
  }

  // 绑定事件
  bindEvents() {
    const self = this

    // 导航按钮事件
    this.container.on('click', '.calendar-nav-btn', function () {
      const action = $(this).data('action')
      if (action === 'prev') {
        self.prevDays()
      } else if (action === 'next') {
        self.nextDays()
      }
    })

    // 日期点击事件
    this.container.on(
      'click',
      '.horizontal-calendar-day:not(.past)',
      function () {
        const dateStr = $(this).data('date')
        const date = new Date(dateStr)
        self.selectDate(date)
      }
    )

    // 价格日历按钮事件
    this.container.on('click', '.price-calendar-btn', function () {
      // 可以在这里添加价格日历的特殊功能
      console.log('价格日历按钮被点击')
    })
  }

  // 前几天
  prevDays() {
    this.currentStartDate.setDate(
      this.currentStartDate.getDate() - this.options.daysToShow
    )
    this.render()
  }

  // 后几天
  nextDays() {
    this.currentStartDate.setDate(
      this.currentStartDate.getDate() + this.options.daysToShow
    )
    this.render()
  }

  // 选择日期
  selectDate(date) {
    this.selectedDate = date
    this.render()

    if (this.options.onDateSelect) {
      this.options.onDateSelect(date, this.getPriceInfo(date))
    }
  }

  // 更新价格数据
  updatePriceData(priceData) {
    this.options.priceData = priceData
    this.render()
  }

  // 获取选中的日期
  getSelectedDate() {
    return this.selectedDate
  }

  // 跳转到指定日期
  goToDate(date) {
    this.currentStartDate = new Date(date)
    this.render()
  }
}

// jQuery 插件形式
$.fn.horizontalPriceCalendar = function (options) {
  return this.each(function () {
    const $this = $(this)
    let instance = $this.data('horizontalPriceCalendar')

    if (!instance) {
      instance = new HorizontalPriceCalendar(this, options)
      $this.data('horizontalPriceCalendar', instance)
    }

    return instance
  })
}

// 全局暴露
window.HorizontalPriceCalendar = HorizontalPriceCalendar
