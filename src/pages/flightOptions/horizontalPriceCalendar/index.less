@import '../../../less/variables.less';

// 横向价格日历组件样式
.horizontal-price-calendar {
  width: 100%;
  background: @gray-0;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  font-family: @font-family-sans-serif;
  overflow: hidden;

  // 日历头部
  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid @gray-e5;
    background: @gray-lightest;

    .calendar-nav-btn {
      width: 36px;
      height: 36px;
      border: 1px solid @gray-d2;
      border-radius: 50%;
      background: @gray-0;
      color: @gray-75;
      font-size: 16px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;

      &:hover {
        border-color: @primary-color;
        color: @primary-color;
        background: @gray-0;
      }

      &:active {
        background: @gray-lightest;
      }
    }

    .price-calendar-btn {
      padding: 8px 16px;
      border: 1px solid @primary-color;
      border-radius: 20px;
      background: @gray-0;
      color: @primary-color;
      font-size: 14px;
      font-weight: @font-weight-bold;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: @primary-color;
        color: @gray-0;
      }
    }
  }

  // 日期列表容器
  &-days {
    display: flex;
    overflow-x: auto;
    padding: 0;

    // 隐藏滚动条
    &::-webkit-scrollbar {
      display: none;
    }
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  // 单个日期项
  &-day {
    flex: 0 0 auto;
    min-width: 120px;
    padding: 1rem 0.75rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border-right: 1px solid @gray-e5;
    position: relative;

    &:last-child {
      border-right: none;
    }

    .day-currency {
      font-size: 12px;
      color: @gray-lighter;
      font-weight: @font-weight-normal;
      margin-bottom: 2px;
    }

    .day-price {
      font-size: 18px;
      font-weight: @font-weight-bold;
      color: @primary-color;
      margin-bottom: 4px;
      line-height: 1.2;
    }

    .day-date {
      font-size: 12px;
      color: @gray-75;
      font-weight: @font-weight-normal;
    }

    // 悬停效果
    &:hover:not(.past) {
      background: @gray-lightest;

      .day-price {
        color: darken(@primary-color, 10%);
      }

      .day-date {
        color: @gray-dark;
      }
    }

    // 今天
    &.today {
      background: lighten(@primary-color, 45%);

      .day-currency {
        color: @primary-color;
      }

      .day-date {
        color: @primary-color;
        font-weight: @font-weight-bold;
      }

      &::after {
        content: '今天';
        position: absolute;
        top: 4px;
        right: 4px;
        font-size: 10px;
        color: @primary-color;
        background: @gray-0;
        padding: 2px 4px;
        border-radius: 8px;
        font-weight: @font-weight-bold;
      }
    }

    // 选中状态
    &.selected {
      background: @primary-color;

      .day-currency {
        color: @gray-0;
      }

      .day-price {
        color: @gray-0;
      }

      .day-date {
        color: @gray-0;
        font-weight: @font-weight-bold;
      }

      &::after {
        color: @primary-color;
        background: @gray-0;
      }
    }

    // 过去的日期
    &.past {
      cursor: not-allowed;
      opacity: 0.5;

      .day-currency {
        color: @gray-lighter;
      }

      .day-price {
        color: @gray-lighter;
      }

      .day-date {
        color: @gray-lighter;
      }
    }
  }
}

// 响应式设计
@media (max-width: @screen-xs) {
  .horizontal-price-calendar {
    border-radius: 0;
    box-shadow: none;

    &-header {
      padding: 0.75rem;

      .calendar-nav-btn {
        width: 32px;
        height: 32px;
        font-size: 14px;
      }

      .price-calendar-btn {
        font-size: 12px;
        padding: 6px 12px;
      }
    }

    &-day {
      min-width: 100px;
      padding: 0.75rem 0.5rem;

      .day-price {
        font-size: 16px;
      }

      .day-currency,
      .day-date {
        font-size: 11px;
      }

      &.today::after {
        font-size: 9px;
        padding: 1px 3px;
      }
    }
  }
}

// 平板适配
@media (min-width: @screen-xs + 1) and (max-width: @screen-md) {
  .horizontal-price-calendar {
    &-day {
      min-width: 110px;

      .day-price {
        font-size: 17px;
      }
    }
  }
}

// 特殊价格样式
.horizontal-price-calendar-day {
  // 高价格突出显示
  &.high-price .day-price {
    color: @red-ed;
  }

  // 低价格突出显示
  &.low-price .day-price {
    color: @green-19;
  }

  // 特价标识
  &.special-price {
    &::before {
      content: '特';
      position: absolute;
      top: 4px;
      left: 4px;
      width: 16px;
      height: 16px;
      background: @yellow-ff;
      color: @gray-0;
      font-size: 10px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: @font-weight-bold;
    }
  }
}

// 加载状态
.horizontal-price-calendar.loading {
  .horizontal-calendar-days {
    opacity: 0.6;
    pointer-events: none;
  }

  .horizontal-calendar-day .day-price {
    background: @gray-e5;
    color: transparent;
    border-radius: 2px;
    animation: pulse 1.5s ease-in-out infinite;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
